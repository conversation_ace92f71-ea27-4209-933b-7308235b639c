/// Splash 页面配置模型
/// 用于从后台获取启动页面的配置信息
class SplashConfig {
  /// 应用名称
  final String appName;
  
  /// 副标题
  final String subtitle;
  
  /// 背景图片 URL
  final String? backgroundImageUrl;
  
  /// 本地背景图片资源路径
  final String? backgroundImageAsset;
  
  /// 显示时长（秒）
  final int displayDuration;
  
  /// 是否显示跳过按钮
  final bool showSkipButton;
  
  /// 版本号
  final String version;
  
  /// 版权信息
  final String copyright;

  const SplashConfig({
    required this.appName,
    required this.subtitle,
    this.backgroundImageUrl,
    this.backgroundImageAsset,
    this.displayDuration = 5,
    this.showSkipButton = true,
    this.version = '1.0.0',
    this.copyright = '© 2024 Faith App',
  });

  /// 从 JSON 创建配置对象
  factory SplashConfig.fromJson(Map<String, dynamic> json) {
    return SplashConfig(
      appName: json['app_name'] ?? 'Faith',
      subtitle: json['subtitle'] ?? '简约 · 优雅 · 现代',
      backgroundImageUrl: json['background_image_url'],
      backgroundImageAsset: json['background_image_asset'],
      displayDuration: json['display_duration'] ?? 5,
      showSkipButton: json['show_skip_button'] ?? true,
      version: json['version'] ?? '1.0.0',
      copyright: json['copyright'] ?? '© 2024 Faith App',
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'app_name': appName,
      'subtitle': subtitle,
      'background_image_url': backgroundImageUrl,
      'background_image_asset': backgroundImageAsset,
      'display_duration': displayDuration,
      'show_skip_button': showSkipButton,
      'version': version,
      'copyright': copyright,
    };
  }

  /// 默认配置
  static const SplashConfig defaultConfig = SplashConfig(
    appName: 'Faith',
    subtitle: '简约 · 优雅 · 现代',
    displayDuration: 5,
    showSkipButton: true,
    version: '1.0.0',
    copyright: '© 2024 Faith App',
  );

  /// 复制并修改配置
  SplashConfig copyWith({
    String? appName,
    String? subtitle,
    String? backgroundImageUrl,
    String? backgroundImageAsset,
    int? displayDuration,
    bool? showSkipButton,
    String? version,
    String? copyright,
  }) {
    return SplashConfig(
      appName: appName ?? this.appName,
      subtitle: subtitle ?? this.subtitle,
      backgroundImageUrl: backgroundImageUrl ?? this.backgroundImageUrl,
      backgroundImageAsset: backgroundImageAsset ?? this.backgroundImageAsset,
      displayDuration: displayDuration ?? this.displayDuration,
      showSkipButton: showSkipButton ?? this.showSkipButton,
      version: version ?? this.version,
      copyright: copyright ?? this.copyright,
    );
  }
}
