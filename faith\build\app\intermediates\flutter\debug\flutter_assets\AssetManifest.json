{"assets/svg/404.svg": ["assets/svg/404.svg"], "packages/cupertino_icons/assets/CupertinoIcons.ttf": ["packages/cupertino_icons/assets/CupertinoIcons.ttf"], "packages/lucide_icons_flutter/assets/build_font/LucideVariable-w100.ttf": ["packages/lucide_icons_flutter/assets/build_font/LucideVariable-w100.ttf"], "packages/lucide_icons_flutter/assets/build_font/LucideVariable-w200.ttf": ["packages/lucide_icons_flutter/assets/build_font/LucideVariable-w200.ttf"], "packages/lucide_icons_flutter/assets/build_font/LucideVariable-w300.ttf": ["packages/lucide_icons_flutter/assets/build_font/LucideVariable-w300.ttf"], "packages/lucide_icons_flutter/assets/build_font/LucideVariable-w400.ttf": ["packages/lucide_icons_flutter/assets/build_font/LucideVariable-w400.ttf"], "packages/lucide_icons_flutter/assets/build_font/LucideVariable-w500.ttf": ["packages/lucide_icons_flutter/assets/build_font/LucideVariable-w500.ttf"], "packages/lucide_icons_flutter/assets/build_font/LucideVariable-w600.ttf": ["packages/lucide_icons_flutter/assets/build_font/LucideVariable-w600.ttf"], "packages/lucide_icons_flutter/assets/lucide.ttf": ["packages/lucide_icons_flutter/assets/lucide.ttf"], "packages/shadcn_ui/fonts/Geist-Black.otf": ["packages/shadcn_ui/fonts/Geist-Black.otf"], "packages/shadcn_ui/fonts/Geist-Bold.otf": ["packages/shadcn_ui/fonts/Geist-Bold.otf"], "packages/shadcn_ui/fonts/Geist-Light.otf": ["packages/shadcn_ui/fonts/Geist-Light.otf"], "packages/shadcn_ui/fonts/Geist-Medium.otf": ["packages/shadcn_ui/fonts/Geist-Medium.otf"], "packages/shadcn_ui/fonts/Geist-Regular.otf": ["packages/shadcn_ui/fonts/Geist-Regular.otf"], "packages/shadcn_ui/fonts/Geist-SemiBold.otf": ["packages/shadcn_ui/fonts/Geist-SemiBold.otf"], "packages/shadcn_ui/fonts/Geist-Thin.otf": ["packages/shadcn_ui/fonts/Geist-Thin.otf"], "packages/shadcn_ui/fonts/Geist-UltraBlack.otf": ["packages/shadcn_ui/fonts/Geist-UltraBlack.otf"], "packages/shadcn_ui/fonts/Geist-UltraLight.otf": ["packages/shadcn_ui/fonts/Geist-UltraLight.otf"], "packages/shadcn_ui/fonts/GeistMono-Black.otf": ["packages/shadcn_ui/fonts/GeistMono-Black.otf"], "packages/shadcn_ui/fonts/GeistMono-Bold.otf": ["packages/shadcn_ui/fonts/GeistMono-Bold.otf"], "packages/shadcn_ui/fonts/GeistMono-Light.otf": ["packages/shadcn_ui/fonts/GeistMono-Light.otf"], "packages/shadcn_ui/fonts/GeistMono-Medium.otf": ["packages/shadcn_ui/fonts/GeistMono-Medium.otf"], "packages/shadcn_ui/fonts/GeistMono-Regular.otf": ["packages/shadcn_ui/fonts/GeistMono-Regular.otf"], "packages/shadcn_ui/fonts/GeistMono-SemiBold.otf": ["packages/shadcn_ui/fonts/GeistMono-SemiBold.otf"], "packages/shadcn_ui/fonts/GeistMono-Thin.otf": ["packages/shadcn_ui/fonts/GeistMono-Thin.otf"], "packages/shadcn_ui/fonts/GeistMono-UltraBlack.otf": ["packages/shadcn_ui/fonts/GeistMono-UltraBlack.otf"], "packages/shadcn_ui/fonts/GeistMono-UltraLight.otf": ["packages/shadcn_ui/fonts/GeistMono-UltraLight.otf"]}