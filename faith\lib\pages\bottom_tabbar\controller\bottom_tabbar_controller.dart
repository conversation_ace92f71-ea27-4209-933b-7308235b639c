import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/tab_item.dart';

/// 底部导航栏控制器
/// 负责管理 TabBar 的状态、动画和页面切换
class BottomTabBarController extends GetxController with GetSingleTickerProviderStateMixin {
  /// 当前选中的索引
  final _currentIndex = 0.obs;
  
  /// Tab 项目列表
  final _tabItems = <TabItem>[].obs;
  
  /// 页面控制器
  late PageController pageController;
  
  /// 动画控制器（用于指示器动画）
  late AnimationController animationController;
  
  /// 指示器位置动画
  late Animation<double> indicatorAnimation;
  
  /// 图标缩放动画控制器列表
  final List<AnimationController> _iconAnimationControllers = [];
  
  /// 图标缩放动画列表
  final List<Animation<double>> _iconAnimations = [];
  
  /// 是否启用动画
  final _animationEnabled = true.obs;
  
  /// 动画持续时间
  final Duration animationDuration = const Duration(milliseconds: 300);
  
  /// 获取当前选中索引
  int get currentIndex => _currentIndex.value;
  
  /// 获取 Tab 项目列表
  List<TabItem> get tabItems => _tabItems;
  
  /// 获取是否启用动画
  bool get animationEnabled => _animationEnabled.value;
  
  /// 静态访问器
  static BottomTabBarController get to => Get.find<BottomTabBarController>();

  @override
  void onInit() {
    super.onInit();
    
    // 初始化默认 Tab 项目
    _tabItems.assignAll(TabItems.defaultItems);
    
    // 初始化页面控制器
    pageController = PageController(initialPage: _currentIndex.value);
    
    // 初始化动画控制器
    animationController = AnimationController(
      duration: animationDuration,
      vsync: this,
    );
    
    // 初始化指示器动画
    indicatorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeInOut,
    ));
    
    // 初始化图标动画控制器
    _initIconAnimations();
    
    // 启动初始动画
    animationController.forward();
  }

  @override
  void onClose() {
    pageController.dispose();
    animationController.dispose();
    
    // 释放图标动画控制器
    for (var controller in _iconAnimationControllers) {
      controller.dispose();
    }
    _iconAnimationControllers.clear();
    _iconAnimations.clear();
    
    super.onClose();
  }

  /// 初始化图标动画
  void _initIconAnimations() {
    _iconAnimationControllers.clear();
    _iconAnimations.clear();
    
    for (int i = 0; i < _tabItems.length; i++) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      );
      
      final animation = Tween<double>(
        begin: 1.0,
        end: 1.2,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ));
      
      _iconAnimationControllers.add(controller);
      _iconAnimations.add(animation);
      
      // 如果是当前选中项，设置为选中状态
      if (i == _currentIndex.value) {
        controller.forward();
      }
    }
  }

  /// 切换到指定索引
  void changeTab(int index) {
    if (index < 0 || index >= _tabItems.length) return;
    if (index == _currentIndex.value) return;
    
    final oldIndex = _currentIndex.value;
    _currentIndex.value = index;
    
    // 页面切换动画
    if (_animationEnabled.value) {
      pageController.animateToPage(
        index,
        duration: animationDuration,
        curve: Curves.easeInOut,
      );
      
      // 图标动画
      _playIconAnimation(oldIndex, index);
    } else {
      pageController.jumpToPage(index);
    }
    
    update();
  }

  /// 播放图标动画
  void _playIconAnimation(int oldIndex, int newIndex) {
    // 旧图标缩小
    if (oldIndex < _iconAnimationControllers.length) {
      _iconAnimationControllers[oldIndex].reverse();
    }
    
    // 新图标放大
    if (newIndex < _iconAnimationControllers.length) {
      _iconAnimationControllers[newIndex].forward();
    }
  }

  /// 获取指定索引的图标动画
  Animation<double> getIconAnimation(int index) {
    if (index < _iconAnimations.length) {
      return _iconAnimations[index];
    }
    return AlwaysStoppedAnimation(1.0);
  }

  /// 设置 Tab 项目列表
  void setTabItems(List<TabItem> items) {
    if (items.isEmpty) return;
    
    _tabItems.assignAll(items);
    
    // 重新初始化动画
    for (var controller in _iconAnimationControllers) {
      controller.dispose();
    }
    _initIconAnimations();
    
    // 如果当前索引超出范围，重置为 0
    if (_currentIndex.value >= items.length) {
      _currentIndex.value = 0;
      pageController.jumpToPage(0);
    }
    
    update();
  }

  /// 更新指定索引的徽章
  void updateBadge(int index, {int? badgeCount, String? badgeText}) {
    if (index < 0 || index >= _tabItems.length) return;
    
    final item = _tabItems[index];
    final updatedItem = TabItem(
      icon: item.icon,
      activeIcon: item.activeIcon,
      title: item.title,
      route: item.route,
      enabled: item.enabled,
      badgeCount: badgeCount,
      badgeText: badgeText,
    );
    
    _tabItems[index] = updatedItem;
    update();
  }

  /// 清除指定索引的徽章
  void clearBadge(int index) {
    updateBadge(index, badgeCount: null, badgeText: null);
  }

  /// 设置动画启用状态
  void setAnimationEnabled(bool enabled) {
    _animationEnabled.value = enabled;
  }

  /// 获取当前选中的 Tab 项目
  TabItem? get currentTabItem {
    if (_currentIndex.value < _tabItems.length) {
      return _tabItems[_currentIndex.value];
    }
    return null;
  }

  /// 根据路由查找 Tab 索引
  int findTabIndexByRoute(String route) {
    for (int i = 0; i < _tabItems.length; i++) {
      if (_tabItems[i].route == route) {
        return i;
      }
    }
    return -1;
  }

  /// 切换到指定路由的 Tab
  void changeTabByRoute(String route) {
    final index = findTabIndexByRoute(route);
    if (index != -1) {
      changeTab(index);
    }
  }
}
