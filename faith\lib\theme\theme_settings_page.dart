import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'colors.dart';
import 'theme_controller.dart';

/// 主题设置页面
/// 提供主题模式和主题颜色的设置界面
class ThemeSettingsPage extends StatelessWidget {
  const ThemeSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(
      builder: (controller) => Scaffold(
        appBar: AppBar(
          title: const Text('主题设置'),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 主题模式设置
              _buildThemeModeSection(controller),
              const SizedBox(height: 24),
              
              // 主题颜色设置
              _buildThemeColorSection(controller),
              const SizedBox(height: 24),
              
              // 主题预览
              _buildThemePreview(),
              const SizedBox(height: 24),
              
              // 主题演示
              _buildDemoSection(),
              const SizedBox(height: 24),

              // 重置按钮
              _buildResetSection(controller),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建主题模式设置区域
  Widget _buildThemeModeSection(ThemeController controller) {
    return ShadCard(
      title: const Text('显示模式'),
      description: const Text('选择亮色或暗色主题'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          
          // 主题模式开关
          Row(
            children: [
              const Text('暗色模式'),
              const Spacer(),
              ShadSwitch(
                value: controller.isDarkMode,
                onChanged: (value) => controller.setThemeMode(value),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 主题模式按钮
          Row(
            children: [
              Expanded(
                child: _buildModeButton(
                  icon: Icons.light_mode,
                  label: '亮色',
                  isSelected: !controller.isDarkMode,
                  onTap: () => controller.setThemeMode(false),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildModeButton(
                  icon: Icons.dark_mode,
                  label: '暗色',
                  isSelected: controller.isDarkMode,
                  onTap: () => controller.setThemeMode(true),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建主题颜色设置区域
  Widget _buildThemeColorSection(ThemeController controller) {
    return ShadCard(
      title: const Text('主题颜色'),
      description: const Text('选择您喜欢的主题颜色'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          
          // 颜色网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1,
            ),
            itemCount: AppThemeColor.values.length,
            itemBuilder: (context, index) {
              final themeColor = AppThemeColor.values[index];
              final isSelected = controller.currentThemeColor == themeColor;
              
              return _buildColorOption(
                color: themeColor.seedColor,
                label: themeColor.displayName,
                isSelected: isSelected,
                onTap: () => controller.setThemeColor(themeColor),
              );
            },
          ),
          const SizedBox(height: 16),
          
          // 当前主题显示
          Builder(
            builder: (context) => Text(
              '当前主题: ${controller.currentThemeDisplayName}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主题预览区域
  Widget _buildThemePreview() {
    return ShadCard(
      title: const Text('主题预览'),
      description: const Text('查看当前主题效果'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          
          // 按钮预览
          Row(
            children: [
              Expanded(
                child: ShadButton(
                  onPressed: () {},
                  child: const Text('主要按钮'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ShadButton.outline(
                  onPressed: () {},
                  child: const Text('次要按钮'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 输入框预览
          const ShadInput(
            placeholder: Text('输入框预览'),
          ),
          const SizedBox(height: 16),
          
          // 徽章预览
          Wrap(
            spacing: 8,
            children: [
              ShadBadge(child: const Text('Primary')),
              ShadBadge.secondary(child: const Text('Secondary')),
              ShadBadge.outline(child: const Text('Outline')),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建演示区域
  Widget _buildDemoSection() {
    return ShadCard(
      title: const Text('主题演示'),
      description: const Text('查看自定义主题的详细效果'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ShadButton(
              onPressed: () => Get.toNamed('/theme-demo'),
              child: const Text('查看主题演示'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建重置区域
  Widget _buildResetSection(ThemeController controller) {
    return ShadCard(
      title: const Text('重置主题'),
      description: const Text('恢复默认主题设置'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ShadButton.outline(
              onPressed: () {
                controller.resetTheme();
                Get.snackbar(
                  '主题重置',
                  '已恢复默认主题设置',
                  snackPosition: SnackPosition.BOTTOM,
                );
              },
              child: const Text('重置为默认主题'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模式按钮
  Widget _buildModeButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Builder(
      builder: (context) => ShadButton.outline(
        onPressed: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Theme.of(context).colorScheme.primary : null,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Theme.of(context).colorScheme.primary : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建颜色选项
  Widget _buildColorOption({
    required Color color,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Builder(
      builder: (context) => GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 3,
                  )
                : null,
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: isSelected
              ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 24,
                )
              : null,
        ),
      ),
    );
  }
}
