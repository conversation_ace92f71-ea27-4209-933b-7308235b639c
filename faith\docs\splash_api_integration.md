# 启动页 API 集成文档

## 概述

启动页系统支持从后台 API 动态获取配置，包括应用名称、副标题、背景图片等。

## 启用真实 API 调用

### 1. 安装 HTTP 依赖

在 `pubspec.yaml` 中添加：

```yaml
dependencies:
  http: ^1.1.0
```

### 2. 取消注释相关代码

在 `lib/pages/splash/services/splash_service.dart` 中：

```dart
// 取消注释这些导入
import 'package:http/http.dart' as http;
import 'dart:convert';

// 取消注释 API 调用代码
final response = await http.get(
  Uri.parse(ApiConfig.splashConfigUrl),
  headers: ApiConfig.defaultHeaders,
).timeout(Duration(seconds: ApiConfig.timeoutSeconds));

if (response.statusCode == 200) {
  final data = json.decode(response.body);
  return data['data']; // 根据实际 API 响应结构调整
}
```

### 3. 配置 API 端点

在 `lib/config/api_config.dart` 中修改：

```dart
static const String baseUrl = 'https://your-actual-api.com';
```

## API 接口规范

### 请求

- **URL**: `GET /api/v1/splash-config`
- **Headers**: `Content-Type: application/json`

### 响应格式

```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "app_name": "Faith Pro",
    "subtitle": "后台配置 · 动态更新",
    "background_image_url": "https://example.com/bg.jpg",
    "background_image_asset": null,
    "display_duration": 5,
    "show_skip_button": true,
    "version": "1.0.0",
    "copyright": "© 2024 Faith Pro App"
  },
  "status_code": 200
}
```

### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_name | String | 否 | 应用名称 |
| subtitle | String | 否 | 副标题 |
| background_image_url | String | 否 | 网络背景图片 URL |
| background_image_asset | String | 否 | 本地资源路径 |
| display_duration | Number | 否 | 显示时长（秒） |
| show_skip_button | Boolean | 否 | 是否显示跳过按钮 |
| version | String | 否 | 版本号 |
| copyright | String | 否 | 版权信息 |

## 错误处理

- API 调用失败时自动使用默认配置
- 网络超时时间为 10 秒
- 所有字段都是可选的，未提供的字段使用默认值

## 页面选择逻辑

系统会根据后台配置自动选择合适的启动页面：

- **有背景图或自定义内容**: 使用 `SplashPageWithBg`
- **无后台配置**: 使用默认的 `SplashPage`

## 测试

在开发环境中，可以通过修改 `_fetchConfigFromApi` 方法返回不同的测试数据来验证功能。
