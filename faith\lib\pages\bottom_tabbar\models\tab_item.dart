import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

/// 底部导航栏项目数据模型
class TabItem {
  /// 图标
  final IconData icon;
  
  /// 选中时的图标（可选，如果不提供则使用同一个图标）
  final IconData? activeIcon;
  
  /// 标题
  final String title;
  
  /// 路由名称或页面标识
  final String route;
  
  /// 是否启用（默认为 true）
  final bool enabled;
  
  /// 徽章数量（可选）
  final int? badgeCount;
  
  /// 徽章文本（可选，优先级高于 badgeCount）
  final String? badgeText;

  const TabItem({
    required this.icon,
    this.activeIcon,
    required this.title,
    required this.route,
    this.enabled = true,
    this.badgeCount,
    this.badgeText,
  });

  /// 获取当前状态下应该显示的图标
  IconData getIcon(bool isActive) {
    if (isActive && activeIcon != null) {
      return activeIcon!;
    }
    return icon;
  }

  /// 是否有徽章
  bool get hasBadge => badgeText != null || (badgeCount != null && badgeCount! > 0);

  /// 获取徽章显示文本
  String get displayBadgeText {
    if (badgeText != null) return badgeText!;
    if (badgeCount != null && badgeCount! > 0) {
      return badgeCount! > 99 ? '99+' : badgeCount.toString();
    }
    return '';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TabItem &&
          runtimeType == other.runtimeType &&
          icon == other.icon &&
          activeIcon == other.activeIcon &&
          title == other.title &&
          route == other.route &&
          enabled == other.enabled &&
          badgeCount == other.badgeCount &&
          badgeText == other.badgeText;

  @override
  int get hashCode =>
      icon.hashCode ^
      activeIcon.hashCode ^
      title.hashCode ^
      route.hashCode ^
      enabled.hashCode ^
      badgeCount.hashCode ^
      badgeText.hashCode;

  @override
  String toString() {
    return 'TabItem{icon: $icon, activeIcon: $activeIcon, title: $title, route: $route, enabled: $enabled, badgeCount: $badgeCount, badgeText: $badgeText}';
  }
}

/// 预定义的 Tab 项目配置
class TabItems {
  /// 默认的 Tab 项目列表
  static const List<TabItem> defaultItems = [
    TabItem(
      icon: LucideIcons.home,
      title: '首页',
      route: 'home',
    ),
    TabItem(
      icon: LucideIcons.compass,
      title: '发现',
      route: 'discover',
    ),
    TabItem(
      icon: LucideIcons.messageCircle,
      title: '消息',
      route: 'message',
      badgeCount: 3, // 示例徽章
    ),
    TabItem(
      icon: LucideIcons.user,
      title: '我的',
      route: 'profile',
    ),
  ];

  /// 获取自定义 Tab 项目列表的示例
  static List<TabItem> getCustomItems() {
    return [
      const TabItem(
        icon: LucideIcons.home,
        activeIcon: LucideIcons.home,
        title: '首页',
        route: 'home',
      ),
      const TabItem(
        icon: LucideIcons.search,
        activeIcon: LucideIcons.search,
        title: '搜索',
        route: 'search',
      ),
      const TabItem(
        icon: LucideIcons.heart,
        activeIcon: LucideIcons.heart,
        title: '收藏',
        route: 'favorites',
        badgeText: 'New',
      ),
      const TabItem(
        icon: LucideIcons.settings,
        activeIcon: LucideIcons.settings,
        title: '设置',
        route: 'settings',
      ),
    ];
  }
}
