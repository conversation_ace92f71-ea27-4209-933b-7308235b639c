import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
// import 'package:http/http.dart' as http; // 取消注释以使用 HTTP 请求
// import 'dart:convert'; // 取消注释以使用 JSON 解析
import '../models/splash_config.dart';
// import '../../../config/api_config.dart';

/// Splash 配置服务
/// 负责从后台获取启动页面的配置信息
class SplashService extends GetxController {
  static SplashService get to => Get.find();

  /// 当前配置
  final _config = SplashConfig.defaultConfig.obs;
  SplashConfig get config => _config.value;

  /// 是否正在加载配置
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    // 应用启动时加载配置
    loadConfig();
  }

  /// 从后台加载配置
  Future<void> loadConfig() async {
    try {
      _isLoading.value = true;
      
      // 模拟从后台 API 获取配置
      // 实际项目中这里应该是真实的 API 调用
      final configData = await _fetchConfigFromApi();
      
      if (configData != null) {
        _config.value = SplashConfig.fromJson(configData);
      }
    } catch (e) {
      // 加载失败时使用默认配置
      debugPrint('加载 Splash 配置失败: $e');
      _config.value = SplashConfig.defaultConfig;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 从后台 API 获取启动页配置
  /// 返回配置数据或 null（使用默认配置）
  Future<Map<String, dynamic>?> _fetchConfigFromApi() async {
    try {
      // 真实的 API 调用示例（取消注释以启用）
      // final response = await http.get(
      //   Uri.parse(ApiConfig.splashConfigUrl),
      //   headers: ApiConfig.defaultHeaders,
      // ).timeout(Duration(seconds: ApiConfig.timeoutSeconds));
      //
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return data['data']; // 根据实际 API 响应结构调整
      // }

      // 临时：模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 500));

      // 临时：返回 null 使用默认配置
      // 当后台 API 准备好后，取消注释上面的代码并删除下面的 return null
      return null;

    } catch (e) {
      // API 调用失败时使用默认配置
      debugPrint('获取启动页配置失败: $e');
      return null;
    }
  }

  /// 更新配置（用于测试或动态更新）
  void updateConfig(SplashConfig newConfig) {
    _config.value = newConfig;
  }

  /// 设置背景图片 URL
  void setBackgroundImageUrl(String url) {
    _config.value = _config.value.copyWith(backgroundImageUrl: url);
  }

  /// 设置应用名称
  void setAppName(String name) {
    _config.value = _config.value.copyWith(appName: name);
  }

  /// 设置副标题
  void setSubtitle(String subtitle) {
    _config.value = _config.value.copyWith(subtitle: subtitle);
  }

  /// 手动刷新配置（用于调试或手动更新）
  Future<void> refreshConfig() async {
    await loadConfig();
  }
}

/// Splash 配置扩展方法
extension SplashConfigExtension on SplashConfig {
  /// 是否有背景图片
  bool get hasBackgroundImage => 
      (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) ||
      (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty);
  
  /// 获取背景图片类型
  String get backgroundImageType {
    if (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) {
      return 'network';
    } else if (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty) {
      return 'asset';
    } else {
      return 'gradient';
    }
  }
}
