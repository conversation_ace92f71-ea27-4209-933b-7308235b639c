import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../controller/bottom_tabbar_controller.dart';
import '../models/tab_item.dart';

/// 动画底部导航栏组件
class AnimatedBottomTabBar extends StatelessWidget {
  /// TabBar 高度
  final double height;
  
  /// 背景颜色（可选，默认使用主题颜色）
  final Color? backgroundColor;
  
  /// 选中项颜色（可选，默认使用主题颜色）
  final Color? selectedColor;
  
  /// 未选中项颜色（可选，默认使用主题颜色）
  final Color? unselectedColor;
  
  /// 是否显示标签文字
  final bool showLabels;
  
  /// 是否启用阴影
  final bool enableShadow;
  
  /// 边框圆角
  final double borderRadius;
  
  /// 内边距
  final EdgeInsets padding;
  
  /// 项目间距
  final double itemSpacing;

  const AnimatedBottomTabBar({
    super.key,
    this.height = 70.0,
    this.backgroundColor,
    this.selectedColor,
    this.unselectedColor,
    this.showLabels = true,
    this.enableShadow = true,
    this.borderRadius = 0.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
    this.itemSpacing = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BottomTabBarController>(
      builder: (controller) {
        final theme = Theme.of(context);
        final colorScheme = theme.colorScheme;
        
        return Container(
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor ?? colorScheme.surface,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(borderRadius),
              topRight: Radius.circular(borderRadius),
            ),
            boxShadow: enableShadow ? [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8.0,
                offset: const Offset(0, -2),
              ),
            ] : null,
            border: Border(
              top: BorderSide(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 0.5,
              ),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: padding,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: List.generate(
                  controller.tabItems.length,
                  (index) => _buildTabItem(
                    context,
                    controller,
                    controller.tabItems[index],
                    index,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建单个 Tab 项目
  Widget _buildTabItem(
    BuildContext context,
    BottomTabBarController controller,
    TabItem item,
    int index,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = controller.currentIndex == index;
    
    final effectiveSelectedColor = selectedColor ?? colorScheme.primary;
    final effectiveUnselectedColor = unselectedColor ?? colorScheme.onSurface.withValues(alpha: 0.6);
    
    return Expanded(
      child: GestureDetector(
        onTap: item.enabled ? () => controller.changeTab(index) : null,
        behavior: HitTestBehavior.opaque,
        child: AnimatedBuilder(
          animation: controller.getIconAnimation(index),
          builder: (context, child) {
            return Transform.scale(
              scale: controller.getIconAnimation(index).value,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 图标和徽章
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        // 图标
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.all(4.0),
                          decoration: isSelected ? BoxDecoration(
                            color: effectiveSelectedColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12.0),
                          ) : null,
                          child: Icon(
                            item.getIcon(isSelected),
                            size: 24.0,
                            color: isSelected 
                                ? effectiveSelectedColor 
                                : (item.enabled ? effectiveUnselectedColor : effectiveUnselectedColor.withValues(alpha: 0.3)),
                          ),
                        ),
                        
                        // 徽章
                        if (item.hasBadge)
                          Positioned(
                            right: -2,
                            top: -2,
                            child: _buildBadge(context, item, effectiveSelectedColor),
                          ),
                      ],
                    ),
                    
                    // 标签文字
                    if (showLabels) ...[
                      const SizedBox(height: 4.0),
                      AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 200),
                        style: theme.textTheme.labelSmall!.copyWith(
                          color: isSelected 
                              ? effectiveSelectedColor 
                              : (item.enabled ? effectiveUnselectedColor : effectiveUnselectedColor.withValues(alpha: 0.3)),
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                        ),
                        child: Text(
                          item.title,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                    
                    // 选中指示器
                    if (isSelected)
                      Container(
                        margin: const EdgeInsets.only(top: 2.0),
                        height: 2.0,
                        width: 20.0,
                        decoration: BoxDecoration(
                          color: effectiveSelectedColor,
                          borderRadius: BorderRadius.circular(1.0),
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建徽章
  Widget _buildBadge(BuildContext context, TabItem item, Color primaryColor) {
    final theme = Theme.of(context);
    final badgeText = item.displayBadgeText;
    
    if (badgeText.isEmpty) return const SizedBox.shrink();
    
    return Container(
      constraints: const BoxConstraints(
        minWidth: 16.0,
        minHeight: 16.0,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.error,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: theme.colorScheme.surface,
          width: 1.0,
        ),
      ),
      child: Text(
        badgeText,
        style: theme.textTheme.labelSmall!.copyWith(
          color: theme.colorScheme.onError,
          fontSize: 10.0,
          fontWeight: FontWeight.w600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
