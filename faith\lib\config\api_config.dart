/// API 配置类
/// 管理所有 API 端点和配置
class ApiConfig {
  /// 基础 URL
  static const String baseUrl = 'https://your-api.com';
  
  /// API 版本
  static const String apiVersion = 'v1';
  
  /// 完整的 API 基础路径
  static String get apiBaseUrl => '$baseUrl/api/$apiVersion';
  
  /// 启动页配置 API 端点
  static String get splashConfigUrl => '$apiBaseUrl/splash-config';
  
  /// 请求超时时间（秒）
  static const int timeoutSeconds = 10;
  
  /// 默认请求头
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  /// 获取带认证的请求头
  /// [token] 用户认证令牌
  static Map<String, String> getAuthHeaders(String? token) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
}

/// API 响应状态码
class ApiStatusCode {
  static const int success = 200;
  static const int created = 201;
  static const int badRequest = 400;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int notFound = 404;
  static const int serverError = 500;
}

/// API 响应模型
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int statusCode;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    required this.statusCode,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : json['data'],
      statusCode: json['status_code'] ?? 200,
    );
  }
}
